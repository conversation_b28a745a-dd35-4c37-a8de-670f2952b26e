<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-box {
            border: 1px solid #ccc;
            height: 300px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #333;
        }
        .initial-display-area {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        .input-area {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .input-area input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .input-area button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-outline-secondary {
            background-color: transparent;
            color: #6c757d;
            border: 1px solid #6c757d;
        }
        .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
        }
        .reset-area {
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Reset Button Test</h1>
    <p>This page tests the reset button functionality from the assistant chat.</p>
    
    <div id="chat-box" class="chat-box">
        <div id="initial-display-area" class="initial-display-area">
            <h3>Welcome to the Test Chat</h3>
            <p>Start a conversation by typing a message below.</p>
        </div>
    </div>
    
    <div class="reset-area">
        <button type="button" id="reset-chat-btn" class="btn-outline-secondary">Reset Conversation</button>
    </div>
    
    <div class="input-area">
        <input type="text" id="message-input" placeholder="Type your message here...">
        <button type="button" id="send-button" class="btn-primary">Send</button>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Type a message and click Send to add messages to the chat</li>
            <li>Click the "Reset Conversation" button</li>
            <li>The chat should clear and show the initial welcome message</li>
            <li>Check the browser console for any JavaScript errors</li>
        </ol>
    </div>

    <!-- Load the button functionality script -->
    <script src="static1/js/button-functionality.js"></script>
    
    <script>
        // Simple chat functionality for testing
        let messageHistory = [];
        
        function addMessage(message, isUser = false) {
            const chatBox = document.getElementById('chat-box');
            const initialDisplay = document.getElementById('initial-display-area');
            
            // Hide initial display when first message is added
            if (initialDisplay && initialDisplay.style.display !== 'none') {
                initialDisplay.style.display = 'none';
            }
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
            messageDiv.textContent = message;
            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
            
            // Add to history
            messageHistory.push({ role: isUser ? 'user' : 'assistant', content: message });
        }
        
        function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (message) {
                addMessage(message, true);
                messageInput.value = '';
                
                // Simulate assistant response
                setTimeout(() => {
                    addMessage(`Echo: ${message}`, false);
                }, 500);
            }
        }
        
        // Set up send button and enter key
        document.getElementById('send-button').addEventListener('click', sendMessage);
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Store initial display area HTML for reset functionality
        window.initialDisplayAreaHTML = document.getElementById('initial-display-area').outerHTML;
        
        console.log('Test page loaded. Reset button should work now.');
    </script>
</body>
</html>
