<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Game Restart Button Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .hidden {
            display: none !important;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }
        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .game-area {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background-color: #f9f9f9;
        }
        .messages-container {
            min-height: 200px;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background-color: white;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <h1>Game Restart Button Test</h1>
    <p>This page tests the restart button functionality from the game.</p>
    
    <div class="game-area">
        <h3>Game Simulation</h3>
        <div id="messages-container" class="messages-container">
            <div class="message">Welcome to the game! This is a test message.</div>
        </div>
        
        <button id="restart-game-button-main" class="btn btn-secondary">
            <i class="fas fa-redo-alt"></i> Restart Game (Main)
        </button>
        
        <button id="restart-game-button-sidebar" class="btn btn-secondary">
            <i class="fas fa-redo"></i> Restart Game (Sidebar)
        </button>
        
        <button id="add-message-btn" class="btn btn-secondary">Add Test Message</button>
    </div>
    
    <!-- Restart Game Confirmation Modal -->
    <div id="restart-confirmation-modal" class="modal hidden">
        <div class="modal-content">
            <h2>Restart Game?</h2>
            <p>Are you sure you want to restart the game? This will clear all your messages and reset your progress.</p>
            <div class="modal-buttons">
                <button type="button" id="cancel-restart-button" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" id="confirm-restart-button" class="btn btn-danger">
                    <i class="fas fa-redo"></i> Yes, Restart Game
                </button>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Click "Add Test Message" to add some messages to the game area</li>
            <li>Click either "Restart Game" button</li>
            <li>A confirmation modal should appear</li>
            <li>Click "Yes, Restart Game" to confirm</li>
            <li>The messages should be cleared and the modal should close</li>
            <li>Check the browser console for any JavaScript errors</li>
        </ol>
    </div>

    <script>
        // Simulate the game state and elements
        const gameState = {
            messages: []
        };
        
        const elements = {
            messagesContainer: document.getElementById('messages-container')
        };
        
        // Show restart game confirmation modal
        function showRestartConfirmation() {
            console.log('Showing restart confirmation modal');
            const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
            if (restartConfirmationModal) {
                restartConfirmationModal.classList.remove('hidden');

                // Make sure the confirm button has the correct event listener
                const confirmRestartButton = document.getElementById('confirm-restart-button');
                if (confirmRestartButton) {
                    // Remove any existing event listeners to prevent duplicates
                    const newConfirmButton = confirmRestartButton.cloneNode(true);
                    confirmRestartButton.parentNode.replaceChild(newConfirmButton, confirmRestartButton);

                    // Add the event listener
                    newConfirmButton.addEventListener('click', () => {
                        console.log('Restart confirmed by user');
                        restartConfirmationModal.classList.add('hidden');

                        // Clear UI messages immediately
                        if (elements.messagesContainer) {
                            elements.messagesContainer.innerHTML = '<div class="message">Game restarted! Welcome back.</div>';
                            console.log('Cleared message container in UI after restart confirmation');
                        }

                        // Reset game state messages array
                        gameState.messages = [];
                        console.log('Reset gameState.messages array after restart confirmation');
                    });
                }
            }
        }
        
        // Initialize event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for restart confirmation modal
            const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
            const cancelRestartButton = document.getElementById('cancel-restart-button');
            
            if (restartConfirmationModal && cancelRestartButton) {
                cancelRestartButton.addEventListener('click', () => restartConfirmationModal.classList.add('hidden'));
            }

            // Add event listeners for the restart game buttons
            const restartGameButtonSidebar = document.getElementById('restart-game-button-sidebar');
            if (restartGameButtonSidebar) {
                restartGameButtonSidebar.addEventListener('click', showRestartConfirmation);
            }

            // Add event listener for the main restart game button
            const restartGameButtonMain = document.getElementById('restart-game-button-main');
            if (restartGameButtonMain) {
                restartGameButtonMain.addEventListener('click', showRestartConfirmation);
            }
            
            // Add test message functionality
            const addMessageBtn = document.getElementById('add-message-btn');
            if (addMessageBtn) {
                addMessageBtn.addEventListener('click', function() {
                    const messagesContainer = document.getElementById('messages-container');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message';
                    messageDiv.textContent = `Test message ${Date.now()}`;
                    messagesContainer.appendChild(messageDiv);
                });
            }
            
            console.log('Test page loaded. Restart buttons should work now.');
        });
    </script>
</body>
</html>
