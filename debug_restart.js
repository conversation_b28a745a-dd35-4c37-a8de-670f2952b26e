// Debug script to test restart button functionality
// Run this in the browser console to test the restart buttons

console.log('=== RESTART BUTTON DEBUG SCRIPT ===');

// Check if elements exist
const restartButtonMain = document.getElementById('restart-game-button-main');
const restartButtonSidebar = document.getElementById('restart-game-button-sidebar');
const restartModal = document.getElementById('restart-confirmation-modal');
const confirmButton = document.getElementById('confirm-restart-button');
const cancelButton = document.getElementById('cancel-restart-button');

console.log('Elements found:');
console.log('- Main restart button:', !!restartButtonMain);
console.log('- Sidebar restart button:', !!restartButtonSidebar);
console.log('- Restart modal:', !!restartModal);
console.log('- Confirm button:', !!confirmButton);
console.log('- Cancel button:', !!cancelButton);

// Check if showRestartConfirmation function exists
console.log('- showRestartConfirmation function:', typeof showRestartConfirmation !== 'undefined');

// Test clicking the restart buttons
if (restartButtonMain) {
    console.log('Testing main restart button click...');
    restartButtonMain.click();
    
    // Check if modal is visible after 1 second
    setTimeout(() => {
        const isModalVisible = restartModal && !restartModal.classList.contains('hidden');
        console.log('Modal visible after main button click:', isModalVisible);
        
        // Hide modal if it's visible
        if (isModalVisible) {
            restartModal.classList.add('hidden');
        }
    }, 1000);
}

// Test the showRestartConfirmation function directly
if (typeof showRestartConfirmation !== 'undefined') {
    console.log('Testing showRestartConfirmation function directly...');
    setTimeout(() => {
        showRestartConfirmation();
        
        setTimeout(() => {
            const isModalVisible = restartModal && !restartModal.classList.contains('hidden');
            console.log('Modal visible after direct function call:', isModalVisible);
            
            // Hide modal if it's visible
            if (isModalVisible) {
                restartModal.classList.add('hidden');
            }
        }, 500);
    }, 2000);
}

// Check event listeners
console.log('Checking event listeners...');
if (restartButtonMain) {
    console.log('Main button event listeners:', getEventListeners ? getEventListeners(restartButtonMain) : 'getEventListeners not available');
}

// Manual test function
window.testRestart = function() {
    console.log('Manual restart test...');
    if (restartModal) {
        restartModal.classList.remove('hidden');
        console.log('Modal should now be visible');
    }
};

console.log('Debug script loaded. You can run testRestart() to manually show the modal.');
